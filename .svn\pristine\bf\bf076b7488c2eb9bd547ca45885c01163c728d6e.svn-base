{"name": "mout", "version": "0.9.0", "main": "src/", "ignore": ["_build", "doc", "tests", ".editorconfig", ".j<PERSON>trc", ".n<PERSON><PERSON><PERSON>", ".travis.yml", "CHANGELOG.md", "CONTRIBUTING.md", "build.js", "package.json"], "homepage": "https://github.com/mout/mout", "_release": "0.9.0", "_resolution": {"type": "version", "tag": "v0.9.0", "commit": "4b360e15328aefe079ee0196f130df5ecc711bf7"}, "_source": "git://github.com/mout/mout.git", "_target": "0.9.0", "_originalSource": "mout"}