body {
  background-color: #eff0f1;
  font-family: Arial !important;
}

/**** Block css **/

.blocks {
  margin-bottom: 34px;
}

li[data-id="inkColor1"][data-type="rgb"] ul.blocks {
  margin-bottom: 15px;
}

.txt_style {
  font-weight: bold;
  font-size: 18px;
  letter-spacing: 0.02em;
}

/***** Buttons css ***/
.button,
.button:hover {
  background: #c44a26;
  color: #ffffff;
  font-family: Arial;
  height: 40px;
  font-size: 13px;
  border-radius: 5px;
  padding: 8px 8px;
}

.button:focus {
  background: #d5763d;
  /* DFS-549 Change*/
  text-decoration: none;
}

@media (hover: hover) and (pointer: fine) {
  .button:hover {
    background: #d5763d;
    text-decoration: none;
  }
}
@media (pointer: coarse) {
  .button:active {
    background: #d5763d;
    text-decoration: none;
  }
}

.button_neutral {
  background: #e5f1f8 none repeat scroll 0 0;
  border: 1px solid #7fa4be;
  color: #005194;
  font-size: 12px;
  font-weight: normal !important;
}

.button_neutral:focus,
.button_neutral:hover {
  background: #bcd5e3;
  font-size: 12px;
}

#button_prev {
  padding-top:10px !important;
  padding-bottom:9px !important;
  font-weight: 700 !important;
  font-size: 12px !important
}
#button_prev:hover {
  padding-top:10px !important;
  padding-bottom:9px !important;
  font-weight: 700 !important;
  font-size: 12px !important;
  border: 1px solid #bcd5e3;
}

.button_file_browse {
  height: 20px;
}

.js-logo-details .hdg_h4,
.js-logo-details .hdg_h4 {
  font-family: Arial !important;
}

.button_grey {
  background: #757575 !important;
}

.proof-button {
  background: #e5f1f8;
  border: 1px solid #004a7d !important;
  width: 100%;
  /* color: #027AB7;	 */
  color: #004a7d;
  font-size: 12px;
  font-weight: 400;
}

.proof-button:hover,
.proof-button:focus {
  background: #014a7f;
  color: #fff !important;
}

.button_grey:focus,
.button_grey:hover {
  background: #757575 !important;
  color: #ffffff !important;
}

/*** Checkbox css ***/

.foldingHelpLabel {
  width: 142px !important;
}

/**** Comment css **/
.commentCopy {
  background-color: rgb(255, 255, 204);
  padding: 10px 12px 10px 12px;
  margin-bottom: 10px;
  font-size: 14px;
}

.commentLabel {
  color: #333;
}

.commentTextArea {
  width: 98%;
  height: 60px;
  resize: none;
  user-select: text;
  -moz-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
}

/*** Design swatch css ***/

.designRadio.isChecked + label > .designSwatchImage {
  border: 2px solid #c44a26;
}

.designSwatchDesc {
  font-family: Arial;
  font-size: 10px;
}

.designBox_active::before,
.designBox_active:hover::before {
  border: 2px solid #c44126 !important;
}

/**** error css ***/
.error {
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 1.075;
  color: #dd1743;
}

.error-subtext {
  float: left;
  color: #dd1743;
}

.error-head {
  color: #dd1743;
  font-family: arial;
  font-size: 14px;
  /*font-weight: bold;*/
  line-height: 1.075;
  margin-bottom: 10px;
}

.vList .error {
  font-family: 'Source Sans Pro', ​sans-serif !important;
}

.vList .error-head {
  font-size: 16px !important;
  line-height: 1.075;
}

/*** Hdg css **/
.hdg {
  color: #231f20;
  margin-bottom: 8px;
  font-family: 'NewsGothic';
  font-weight: bold;
  letter-spacing: 1px;
}

.hdg_h1 {
  font-family: 'NewsGothic';
  font-weight: normal;
}

.hdg_h2 {
  /* color: #c44a26 !important; */
  color: #a12408 !important;
  font-size: 17px;
  letter-spacing: 0px;
}

.hdg_sub_h2 {
  font-size: 16px;
  margin-bottom: 8px;
}

.price {
  color: #dd1743 !important;
  font-family: arial;
  font-weight: normal !important;
  font-size: 20px !important;
}

.mix-hdg_red {
  color: #d61120;
}

.mix-hdg_blue {
  color: #259cda !important;
}

.media-body {
  font-family: Arial;
}

.js-logo-controls .hdg {
  margin-bottom: 16px !important;
}

/**** Input box css **/
.inputBox {
  font-family: Arial;
  font-size: 15px;
}

/* Link css */
.link {
  text-decoration: none;
  color: #005194;
  cursor: pointer;
  font-family: Arial;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
}

.link_button {
  display: inline-block;
  padding: 7px 16px;
  border: none;
  background: none;
  font-family: Arial;
  font-size: 13px;
  font-weight: 700;
  vertical-align: middle;
  text-decoration: none !important;
}

.green {
  color: #749c38 !important;
}

/* radio css */
.radio + label {
  color: #027ab7;
  font-family: Arial;
}

.radio.isChecked + label {
  font-family: Arial;
}

.radio + label {
  font-size: 14px;
}

.radio + label:before {
  background-position: 0px -48px;
}

.radio.isChecked + label:before {
  background-position: 0 -73px;
}

/* site.css */
.site {
  border: 2px solid #d9e0e5;
  background: #ffffff;
}

.site-hd-right {
  padding: 0px 15px 0px 15px !important;
}

.site-hd {
  background: #f9f9f9;
  height: 100px;
  width: 100%;
}

/* Remove as this is in the common site.css */
@media (min-width: 767px) {
.fbtTitle {
  padding: 0px 0px 10px 100px;
  }
}

.progressBlocks {
  margin: 0 0 0 0;
}

.progressBlocks .isActive {
  color: #005194;
}

[data-id='HIDDEN_LOGO'] {
  margin-top: 5px !important;
}

[data-id='LS'] {
  margin-top: 20px !important;
}

[data-id='SL'] {
  margin-top: 20px !important;
}

.margin-11px {
  margin-bottom: 11px;
}

.margin-14px {
  margin-bottom: 14px;
}

.vList_std .error .grid-col_3of10 {
  padding-top: 0px !important;
}

.site-bd .grid-col_3of10 {
  padding-top: 20px;
}

.media-body .hdg {
  margin-bottom: 0px !important;
}

.js-proofmodalHeading {
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 26px;
  color: #c44a26;
}

.popup-outer {
  background-color: #fff !important;
}

.fancybox-inner {
  background-color: #fff !important;
}

.fancybox-skin {
  position: relative;
  border-radius: 10px;
  background-color: #333;
  border: 10px solid #333;
  z-index: -2;
}

.fancybox-skin:after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: #fff;
  z-index: -1;
  top: 0px;
  left: 0px;
}

.calcelbox {
  float: right;
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 14px;
  color: #3174d8;
  text-align: center;
  cursor: pointer;
}

.js-proofmodalContent1 p {
  font-family: 'Arial Regular', 'Arial';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: #333333;
  text-align: left;
  line-height: normal;
  padding-top: 10px;
}

.js-proofmodalContent2 p {
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 14px;
  color: #231f20;
}

.js-proofmodalContent3 label {
  font-family: 'Arial Regular', 'Arial';
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  color: #333333;
  text-align: left;
  line-height: normal;
}

.js-proofmodalContent3 p {
  font-family: 'Arial Regular', 'Arial';
  font-weight: 400;
  font-style: normal;
  font-size: 15px;
  color: #333333;
  text-align: left;
  line-height: normal;
  margin-left: 28px;
  margin-top: 4px;
  width: 100%;
}

.js-proofmodalContent3 p span {
  font-weight: 800;
}

.cancelbtn {
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 14px;
  color: #3174d8;
  text-align: center;
  padding-left: 40px;
  cursor: pointer;
}

.download-box {
  float: left;
  cursor: pointer;
}

.cancel-box {
  float: left;
  padding-top: 13px;
}

.proof-actions {
  margin: 20px 0px;
  float: left;
}

.btn-download {
  width: 215px;
  padding: 13px 0px;
  cursor: pointer;
  color: #fff !important;
}

.btn-active {
  background: #027ab7 !important;
}

.proof-actions .link-download {
  pointer-events: none;
}

.proof-actions .link-download-active {
  pointer-events: all;
  display: block;
}

.make-visible {
  display: block !important;
}

.js-proofmodalContainer {
  float: left;
  margin-bottom: 15px;
}

.js-proofmodalImager {
  float: left;
}

.js-proofmodalContent {
  float: left;
  margin-left: 15px;
  font-family: 'Arial Regular', 'Arial';
  font-size: 14px;
  line-height: normal;
}

.js-proofmodalContentBold {
  font-weight: bold;
  margin-bottom: 5px;
}

.js-proofmodalContentList ul {
  margin: 5px 0px 0px 25px;
  padding: 0px;
  list-style: inherit;
}

/* step.css */
.step {
  color: #c44a26;
  line-height: 1;
  font-weight: normal;
  text-transform: none;
  font-family: 'NewsGothicBold';
}

.proof-content {
  margin: 15px;
}

.proof-text {
  margin-bottom: 10px;
}

.proof-content p {
  font-family: 'Arial Italic', 'Arial';
  font-weight: 400;
  font-style: italic;
  font-size: 11px;
  line-height: 13px;
}

#basic-modal-content {
  display: none;
}

/* Overlay */
#simplemodal-overlay {
  background-color: #000;
  cursor: wait;
}

/* Container */
#simplemodal-container {
  height: 360px;
  width: 600px;
  color: #bbb;
  background-color: #333;
  border: 4px solid #444;
  padding: 12px;
}

#simplemodal-container .simplemodal-data {
  padding: 8px;
}

#simplemodal-container code {
  background: #141414;
  border-left: 3px solid #65b43d;
  color: #bbb;
  display: block;
  font-size: 12px;
  margin-bottom: 12px;
  padding: 4px 6px 6px;
}

#simplemodal-container a {
  color: #ddd;
}

#simplemodal-container a.modalCloseImg {
  background: url(../img/basic/x.png) no-repeat;
  width: 25px;
  height: 29px;
  display: inline;
  z-index: 3200;
  position: absolute;
  top: -15px;
  right: -16px;
  cursor: pointer;
}

#simplemodal-container h3 {
  color: #84b8d9;
}

/* tooltip css */
.popover {
  width: 445px;
  background-color: #aedfe8;
}

#CopiesHelp,
#NumberingHelp {
  margin-bottom: -4px;
  margin-left: 0px !important;
}

.popover .arrow1,
.popover .arrow1:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.popover .arrow1 {
  border-width: 11px;
}

.popover .arrow1:after {
  border-width: 10px;
  content: '';
}

.popover.bottom .arrow1 {
  margin-left: 342px;
  border-bottom-color: #fff;
  border-top-width: 0;
  top: -11px;
}

.popover.bottom .arrow1:after {
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #aedfe8;
  border-top-width: 0;
  content: ' ';
}

/* txt css */
.txtLarge {
  font-family: arial;
  font-size: 15px;
  cursor: auto !important;
}

/* uploading css */
.progressbar {
  background: #e9e9e9 none repeat scroll 0 0;
  border-radius: 11px;
  height: 13px;
  padding-left: 3px;
  padding-right: 3px;
  padding-top: 3px;
  width: 100%;
}
.progress {
  background: #c34924;
  border-radius: 11px;
  height: 10px;
}

#viewZoomText {
  margin-left: 6px;
  text-align: center;
  width: 24px;
}

.logo-option-button-CUSTOM, 
.logo-option-button-CUSTOM:hover,
#UploadLogo-BrowseForFile  {
  padding-top:14px !important;
  max-height:40px;
}

.fs12{
  font-size: 12px !important;
}




@media (max-width: 991px) {
  .live-chat{
    display: none !important;
    visibility: hidden !important;
  }
  .QSIFeedbackButton {
    display: none !important;
    visibility: hidden !important
  }
  .QSIFeedbackButton div {
    display: none !important;
    visibility: hidden !important
  }
}