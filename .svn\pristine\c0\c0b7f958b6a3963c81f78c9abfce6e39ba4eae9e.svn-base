define(function(require) {
    'use strict';

    var $ = require('jquery');
    var Classes = require('../constants/Classes');
    var EventController = require('./Event');
    var TrackEvents = require('../constants/TrackEvents');
    var bindAll = require('mout/object/bindAll');
    var mixIn = require('mout/object/mixIn');
    var pluck = require('mout/array/pluck');
    var q = require('q');

    /**
     * Determines whether a reference is defined or not. Useful for reducing
     * arrays to only contain defined values.
     *
     * @type {Function}
     * @param {Any} obj
     * @return Boolean
     */
    var isDefined = function(obj) {
        return obj !== undefined;
    };

    /**
     * Default controller configuration values.
     *
     * @type {Object}
     */
    var defaults = {
        view: document.documentElement
    };

    /**
     * @class App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function Controller(config) {
        config = mixIn({}, defaults, config);

        bindAll(this,
            'init',
            'render',
            'cacheElements',
            'attachEvents',
            'detachEvents',
            'start',
            'fbt_start',
            '_start',
            'onError'
        );

        /**
         * @property $view
         * @type {jQuery}
         */
        this.$view = $(config.view);

        /**
         * @property index
         * @type {Object}
         */
        this.index = config.index || {};

        /**
         * @property model
         * @type {Object.<String,App.Models.Abstract>}
         */
        this.model = config.model || {};

        /**
         * @property registry
         * @type {Object.<String,App.Controllers.Controller>}
         */
        this.registry = Object.create(this.registry || {});

        /**
         * @property started
         * @type {Array.<App.Controllers.Controller>}
         */
        this.started = [];

        /**
         * @property ready
         * @type {Promise}
         */
        this.ready = q
            .fcall(this.init)
            .then(this.render)
            .then(this.cacheElements)
            .then(this.attachEvents)
            .then(this.callAfterAll)
            .fail(this.onError);
    }

    var proto = Controller.prototype;

    /**
     * No-op default.
     *
     * @method init
     * @chainable
     */
    proto.init = function() {
        return this;
    };

    /**
     * No-op default.
     *
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = function() {
        return '';
    };

    /**
     * Highly simplistic default renderer. Likely to be overridden.
     *
     * @method render
     * @chainable
     */
    proto.render = function() {
        var content;
        var template = this.template;
        this.model.baseAppUrl = baseAppUrl;
        this.model.host_url = host_url;
        if (typeof template === 'function') {
            content = this.template(this.model);
        }

        if (content) {
            this.$view.html(content);
        }

        return this;
    };

    /**
     * No-op default.
     *
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        return this;
    };

    /**
     * No-op default.
     *
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        return this;
    };

    /**
     * No-op default.
     *
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        return this;
    };

     /**
      * No-op default.
      *
      * @method callAfterAll
      * @chainable
      */
     proto.callAfterAll = function() {
        return this;
     };
    
    /**
     * Finds all `<div data-controller="[name]">` tags in the current view. For
     * each element, a new instance of a controller is created for the element,
     * if a controller with a matching `name` is in this controller's registry
     * and the element is not already started.
     *
     *     // Template
     *     <div class="FooController"></div>
     *
     *     // Registry
     *     this.registry = { FooController: require('./Foo') };
     *
     * @method start
     * @chainable
     */
    proto.start = function() {
        this.started = this.$view
            .find(Classes.CONTROLLER_UNSTARTED_SELECTOR)
            .map(this._start)
            .filter(isDefined)
            .get();

        // Only continue when all children are ready
        return q.all([pluck(this.started, 'ready')]);
    };

    proto.fbt_start = function() {
        var productWrap = $('.product-container');
        // rewrite the DOM to kill all event attached
        productWrap.html('<div data-controller="ProductController"></div>');
        this.started = productWrap
            .find(Classes.CONTROLLER_UNSTARTED_SELECTOR)
            .map(this._start)
            .filter(isDefined)
            .get();
        
        // Only continue when all children are ready
        return q.all([pluck(this.started, 'ready')]);
    };

    /**
     * @method _start
     * @param {Number} i
     * @param {HTMLElement} view
     * @return {App.Controllers.Controller}
     */
    proto._start = function(i, view) {
        var model;
        var $view = $(view);
        var type = $view.data('controller');
        var id = $view.data('id');

        var index = this.index[type];
        var Controller = this.registry[type];

        if (!Controller) {
            return;
        }

        if (index) {
            if (index.getById) {
                model = index.getById(id);
            } else {
                model = index[id];
            }
        }

        return new Controller({
            view: $view.data('started', true),
            model: model || $view.data()
        });
    };

    /**
     * @method use
     * @param {String|Object} name
     * @param {Object|undefined} object
     * @chainable
     */
    proto.use = function(name, object) {
        if (object === undefined) {
            object = name;
            name = object.name;
        }

        this.registry[name] = object;

        return this;
    };

    /**
     * TODO: Handle controller errors.
     *
     * @method onError
     * @param {Any} error
     * @callback
     */
    proto.onError = function(error) {
        var stack = error && error.stack;
        console.error(stack || error);
    };


    /**
     * Determine if controller should be displayed
     *
     * @method isDisplayable
     * @returns {Boolean}
     */
    proto.isDisplayable = function() {
        var i = 0;
        var length = this.started.length;

        if (!length) {
            return false;
        }

        var value = true;

        for (; i < length; i++) {
            value &= this.started[i].isDisplayable();
        }

        return value;
    };

    /**
     * @method track
     * @param {String} pageName
     * @chainable
     */
    proto.track = function(args) {
        var state = this.state || (this.stateModel ? this.stateModel.getState() : '');
        var controller = state && state.controller;
        var model = (controller && controller.model) || state;
        var trackEvent = model && model.trackEvent;
        var skuId = (this.query && this.query.skuId) ? this.query.skuId : (this.model.query && this.model.query.skuId) ? this.model.query.skuId : "";
        // Allow passing a blank eventName
        var eventName = args.eventName === false ? '' : args.eventName || (trackEvent && trackEvent.omniture) || '';
        var pageName = args.pageName || '';
        var list1 = args.list1;
        var prodId = $('input[name=productId].selected').val() ? $('input[name=productId].selected').val() : skuId;
        EventController.emit(TrackEvents.CHANGE, {
            linkName: model.description,
            linkEvents: eventName,
            linkVars: {
                products: ';' + prodId,
                pageName: pageName,
                list1: list1
            }
        });

        return this;
    };

    return Controller;
});
