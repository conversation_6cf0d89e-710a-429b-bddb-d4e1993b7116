/**
 * The script that decides the location of configurator loader_path
 * Author: <PERSON><PERSON>
 * Created: 9th FEB 2017
 **/
var host_name = window.location.hostname;
if (typeof envId !== 'undefined') {
    host_name = envId;
}
var loader_path;

if (typeof window !== 'undefined') {
    if (host_name == 'localhost' || host_name == 'test.localhost.com') {
        loader_path = 'http://localhost:8080/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'sd.localhost.com') {
        loader_path = 'http://sd.localhost.com:8080/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'sf-dev.deluxe.com' || host_name == 'sf-dev.deluxe.com' ) {
        loader_path = 'https://sf-dev.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'sf-stage.deluxe.com' || host_name == 'sf-stage.deluxe.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www.deluxe.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'origin-sf-prod.deluxe.com') {
        loader_path = 'https://origin-sf-prod.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www-dr.deluxe.com') {
        loader_path = 'https://www-dr.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'dfs.localhost.com') {
        loader_path = 'http://dfs.localhost.com:8080/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'b2b-dev.dfsonline.com' || host_name == 'b2b-dev.dfsonline.com') {
        loader_path = 'https://sf-dev.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'b2b-stage.dfsonline.com' || host_name == 'b2b-stage.dfsonline.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www.dfsonline.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'origin-www.dfsonline.com') {
        loader_path = 'https://origin-sf-prod.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www-dr.dfsonline.com') {
        loader_path = 'https://www-dr.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staplescustomprinting.dev.btobsource.com') {
        loader_path = 'https://sf-dev.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staplescustomprinting.qa.btobsource.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staplescustomprinting.btobsource.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staplescustomprintingretail.dev.btobsource.com') {
        loader_path = 'https://sf-dev.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staplescustomprintingretail.qa.btobsource.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staplescustomprintingretail.btobsource.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'dev.printez.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www.printez.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'dev.smartresolution.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staging.smartresolution.com') {
        //loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www.smartresolution.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'caas.dev.btobsource.com') {
        loader_path = 'https://sf-dev.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'caas.qa.btobsource.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'caas.btobsource.com' ) {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'msbc.dev.btobsource.com') {
        loader_path = 'https://sf-dev.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'msbc.qa.btobsource.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www.mysoftwarechecks.com' || host_name == 'mysoftwarechecks.com' || host_name == 'www.microsoftbusinesschecks.com' || host_name == 'microsoftbusinesschecks.com' || host_name == 'msbc.btobsource.com' ) {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staging-www.holidaycardwebsite.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'staging-www.4printing.com') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name.indexOf('.4printing.com') != -1) {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'tenenz.localhost.com') {
        loader_path = 'http://tenenz.localhost.com:8080/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'tenenz.test') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'www.tenenz.com') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'pol-sbx') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'pol-prd') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'sf-pol-sbx') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'sf-pol-prd') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'otis-sbx') {
        loader_path = 'https://sf-stage.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    } else if (host_name == 'otis-prd') {
        loader_path = 'https://www.deluxe.com/webasset/w2p_mobile/';
        baseAppUrl = 'Release_122.0/';
    }
    // document.write('<script type="text/javascript" src="' + loader_path + baseAppUrl + 'loader.js"/></' + 'script>');

    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = loader_path + baseAppUrl + 'loader.js';
    document.head.appendChild(script);
    document.body.style.height = 'auto';
}
