define(function(require) {
    'use strict';

    var AbstractQuestionController = require('./Abstract');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var DomEvents = require('../../constants/DomEvents');
    var EventController = require('controllers/Event');
    var ProductEvents = require('constants/ProductEvents');
    var Settings = require('../../constants/Settings');
    var Product = require('../../models/Product');
    var Surcharge = require('util/Surcharge');
    var SwatchModel = require('../../models/Swatch');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Classes = require('../../constants/Classes');
    var Helper = require('../../util/helper');

    /**
     * @class App.Controllers.Question.Swatch
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Question} config.model
     */
    function SwatchQuestionController(config) {
        bindAll(this,
            'onChange',
            'onProductChange',
            'onQtyChange',
            'onActionClick',
            'onButtonClick');

        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(SwatchQuestionController, AbstractQuestionController);

    var plmfpf = null;

    /**
     * Do not display question if it only has a single option
     *
     * @property HIDE_SINGLE_ANSWER_QUESTIONS
     * @type Boolean
     */
    proto.HIDE_SINGLE_ANSWER_QUESTIONS = true;

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/swatch');

    var cv_flag = false;

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        //console.log('JoeTest.swatch.render');
        var model = this.model;
        if (model.id === 'matrix1') {
            return this.renderMatrix1();
        }
        if (model.id === 'envelope') {
            proto.HIDE_SINGLE_ANSWER_QUESTIONS = false;
            return this.renderEnvelope();
        }
        return this.renderDefault();
    };


    /**
     * @method renderEnvelope
     * @chainable
     */
    proto.renderEnvelope = function() {
      //console.log('JoeTest.swatch.renderDefault');
        var model = this.model;
        var id = model.id;
        var info = model.info;
        var options = model.info.option;
        var value = model.getValue() || (info && info['default']);
        var hasSelfSealEnvelope = false;
        var hasFoilLinedEnvelope = false;
        var hasFoilLinedSelfSealEnvelope = false;
        var selfSealEnvelopePrice = 0.00;
        var foilLinedEnvelopePrice = 0.00;
        var foilLinedSelfSealEnvelopePrice = 0.00;
        var hasShimmerKwikSealEnvelope = false;
        var shimmerKwikSealEnvelopePrice = 0.00;
        //console.log('JoeTest.swatch.renderDefault.value: ' + value);
        var envTitle = [];
        var envCode = [];
        if (options && options.length) {
            for (var i=0; i<options.length; i++) {
                options[i].imgSrc = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + options[i].swatch + "?wid=50";
                options[i].slug = options[i].id;
                options[i].matrix = {};
                options[i].matrix.uid = options[i].id;
                options[i].uiDesc = options[i].desc;
                envCode.push(options[i].id);
                envTitle.push(options[i].desc);
                if (typeof options[i].surcharge !== 'undefined') {
                    if (Surcharge.getUnitPrice(options[i].surcharge.id, true) != 0) {
                        options[i].uiDesc = options[i].desc + ' (+' + Surcharge.getPrice(options[i].surcharge.id, false, this.pdtQty) + ')';
                    }
                }

                //If matrix extensions are defined in Settings, use the ext attributes
                if (Settings.MATRIX_EXT[options[i].code]) {
                    var chargeId = null;
                    var unitPrice = 0.00;
                    if (Settings.MATRIX_EXT[options[i].code].TYPE === 'FREG') {
                        if (options[i].surcharge) {
                            chargeId = options[i].surcharge.id;
                            unitPrice = Surcharge.getUnitPrice(chargeId, true);
                            foilLinedEnvelopePrice = unitPrice;
                            if (unitPrice > 0) {
                                hasFoilLinedEnvelope = true;
                            }
                        }
                    } else if (Settings.MATRIX_EXT[options[i].code].TYPE === 'FPS') {
                        if (options[i].surcharge) {
                            chargeId = options[i].surcharge.id;
                            unitPrice = Surcharge.getUnitPrice(chargeId, true);
                            foilLinedSelfSealEnvelopePrice = unitPrice;
                            if (unitPrice > 0) {
                                hasFoilLinedSelfSealEnvelope = true;
                            }
                        }
                    } else if (Settings.MATRIX_EXT[options[i].code].TYPE === 'GW') {
                        if (options[i].surcharge) {
                            chargeId = options[i].surcharge.id;
                            unitPrice = Surcharge.getUnitPrice(chargeId, true);
                            shimmerKwikSealEnvelopePrice = unitPrice;
                            if (unitPrice > 0) {
                                hasShimmerKwikSealEnvelope = true;
                            }
                        }
                    } else if (Settings.MATRIX_EXT[options[i].code].TYPE === 'PS') {
                        if (options[i].surcharge) {
                            chargeId = options[i].surcharge.id;
                            unitPrice = Surcharge.getUnitPrice(chargeId, true);
                            selfSealEnvelopePrice = unitPrice;
                            if (unitPrice) {
                                hasSelfSealEnvelope = true;
                            }
                        }
                    } 
                    options[i].imgSrc = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + Settings.MATRIX_EXT[options[i].code].IMG + "?wid=50";
                }
            }
            // if(hasFoilLinedSelfSealEnvelope)
            //     hasSelfSealEnvelope = false;

        }
        //console.log('Swatch.envelopeUpgradePrices - ' + selfSealEnvelopePrice + " - " + foilLinedEnvelopePrice + ' - ' + foilLinedSelfSealEnvelopePrice + ' -');

        window.envCodeVal = envCode;
        window.envTitleVal = envTitle;

        //Preselect the values in addtoCart
        var selectedvalue = this.$view.find('input:checked').val();
        SwatchModel.peelAndSeal = selectedvalue;
        EventController.emit(ProductEvents.ENVELOPE_UPGRADE_CHANGE, value);

        this.$view
            .html(this.template({
                id: id,
                layoutClass: 'blocks_3up',
                desc: model.desc,
                isRequired: model.isRequired,
                'default': value,
                options: options,
                envelope: true,
                hasSelfSealEnvelope: hasSelfSealEnvelope,
                hasFoilLinedEnvelope: hasFoilLinedEnvelope,
                hasFoilLinedSelfSealEnvelope: hasFoilLinedSelfSealEnvelope,
                hasShimmerKwikSealEnvelope: hasShimmerKwikSealEnvelope,
                selfSealEnvelopePrice: selfSealEnvelopePrice,
                foilLinedEnvelopePrice: foilLinedEnvelopePrice,
                foilLinedSelfSealEnvelopePrice: foilLinedSelfSealEnvelopePrice,
                shimmerKwikSealEnvelopePrice: shimmerKwikSealEnvelopePrice,
                baseAppUrl: baseAppUrl,
                host_url: host_url
            }));

        return this;
    };


    /**
     * @method renderDefault
     * @chainable
     */
    proto.renderDefault = function() {
        //console.log('JoeTest.swatch.renderDefault');
        var model = this.model;
        var id = model.id;
        var info = model.info;
        var options = model.options;
        var value = model.getValue() || (info && info['default']);
        //console.log('JoeTest.swatch.renderDefault.value: ' + value);

        this.$view
            .html(this.template({
                id: id,
                layoutClass: 'blocks_3up',
                desc: model.desc,                
                isRequired: model.isRequired,
                'default': value,
                options: options && options._items,
                baseAppUrl: baseAppUrl,
                host_url: host_url
            }));

        return this;
    };

    /**
     * @method renderMatrix1
     * @chainable
     */
    proto.renderMatrix1 = function() {
        // console.log('JoeTest.swatch.renderMatrix1');
        var $view = this.$view;
        var model = this.model;
        var id = model.id;
        var info = model.info;
        // console.log("JoeTest.swatch.renderMatrix1.mode==: ", model);
        //console.log("JoeTest.swatch.renderMatrix1.options: " + JSON.stringify(model.options, undefined, 4));
        
        var options = model.options;
        var standardOptions = [];
        var premiumOptions = [];
        var value = model.getValue() || (info && info['default']);

0
        //console.log('JoeTest.swatch.renderMatrix1.mddel.getValue: ' + model.getValue());
        //console.log('JoeTest.swatch.renderMatrix1.info[default]: ' + info['default']);
        //console.log('JoeTest.swatch.renderMatrix1.default: ' + value);
        //console.log('JoeTest.swatch.renderMatrix1.....................................');
        //console.log('JoeTest.swatch.renderMatrix1.options: ' + options.length());
        //console.log('JoeTest.swatch.renderMatrix1.isDoubleMatrix: ' + options[0].option);
        //console.log('JoeTest.swatch.renderMatrix1.....................................');
        //console.log('JoeTest.swatch.renderMatrix1.info: ' + JSON.stringify(info, undefined, 4));

        var isHighSecurityCheck = false;
        if (this.plmfpf) {
            var plmf = this.plmfpf.substring(0, 4);
            //console.log("Swatch.js.plmf: " + plmf);
            isHighSecurityCheck = $.inArray(plmf, Settings.HIGH_SECURITY_PLMF) > 0;
        }
        //console.log("Swatch.js.isHighSecurityCheck: " + isHighSecurityCheck);
        var matrixTitle = [];
        var matrixCode = [];
        var foundDefault = false;
        var hasTwoMatrix = false;
        var checkFlag = false;
        options.each(function(option) {
            var matrix = option.getMatrix();

            // Ignore
            if (!matrix) { return; }

            // Expose specific matrix data
            option = Object.create(option);
            option.matrix = matrix;
            option.imgSrc = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + matrix.swatch + "?wid=50";
            option.code = matrix.code;
            //console.log("JoeTest.swatch.renderMatrix1.option: " + option.code);
            //console.log("JoeTest.swatch.renderMatrix1.option: " + JSON.stringify(matrix, undefined, 4));
            //console.log("JoeTest.swatch.renderMatrix1.option: " + option.toString());
            //console.log("JoeTest.swatch.renderMatrix1.option: " + JSON.stringify(matrix, undefined, 4));
            option.uiDesc = matrix.desc;
            if (matrix.desc2) {
                hasTwoMatrix = true;
                option.uiDesc += " / " + matrix.desc2;
                option.imgSrc = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + matrix.swatch2 + "?wid=50";
            }
            option.tlId = matrix.swatch;
            matrixCode.push(option.code);
            matrixTitle.push(option.tlId);
            window.matrixCodeValue = matrixCode;
            window.matrixTitleVal = matrixTitle;

            //console.log("JoeTest.swatch.renderMatrix1.option.matrix.uid: " + matrix.uid);
            if (option.code === matrix.uid) {
                foundDefault = true;
            }
            if (option.code === value || matrix.uid === value) {
                checkFlag = true;
            }

            // Determine grouping
            if (option.surcharge) {
                if (isHighSecurityCheck) {
                    option.imgSrc = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + matrix.swatch + "_premium?fmt=jpg";
                    option.uiDesc = "";
                }
                premiumOptions.push(option);
                premiumOptions.sort(function(a, b) { if(a.code < b.code) return -1;
                    if(a.code > b.code) return 1;
                    return 0;
                });
            } else {
                standardOptions.push(option);
            }
        });

        //console.log('jiby', options);
        $view.empty();

        if (premiumOptions.length) {
            let val = Surcharge.getPrice(premiumOptions[0].surcharge.id, false,this.pdtQty)
            var premiumDesc = Content.get('MATRIX1_PREMIUM_LABEL');
            premiumDesc +=  val != '' ? ' (+' + val + ')' : "";
            var layoutClass = 'blocks_4up';
            if (isHighSecurityCheck) {
                premiumDesc = Content.get('MATRIX1_PREMIUM_CHARITY_LABEL');
                premiumDesc +=  val != '' ? ' (+' + val + ')' : "";
                    // Content.get('MATRIX1_PREMIUM_CHARITY_LABEL', {
                    //     value: Surcharge.getPrice(premiumOptions[0].surcharge.id, false,this.pdtQty)
                    // });
                layoutClass = '';
            }
            $view.append(this.template({
                id: id,
                layoutClass: layoutClass,
                desc: premiumDesc,
                first_word: premiumDesc.split(" ")[0],
                isRequired: model.isRequired,
                'default': value,
                options: premiumOptions,
                baseAppUrl: baseAppUrl,
                host_url: host_url
            }));
        }
        if (standardOptions.length) {
            if(!checkFlag) {    
                if(standardOptions.some(function(option) { return option.code == model.info.default } )) {
                    this.model.setValue(model.info.default);
                } else {
                    this.model.setValue(standardOptions[0].code)
                }                
            }
            var matrix1Desc = Content.get('MATRIX1_STANDARD_LABEL');
            if (hasTwoMatrix) {
                matrix1Desc = Content.get('MATRIX2_STANDARD_LABEL');
            }
            $view.append(this.template({
                id: id,
                layoutClass: 'blocks_4up',
                desc: matrix1Desc,
                first_word: matrix1Desc.split(" ")[0],
                isRequired: model.isRequired,
                'default': value,
                options: standardOptions,
                baseAppUrl: baseAppUrl,
                host_url: host_url
            }));
        }


        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        //console.log('JoeTest.swatch.attachEvents');
        this.$view
            .on(DomEvents.CHANGE, this.onChange)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);

        if (this.model.id === 'matrix1' || this.model.id === 'envelope') {
            EventController
                .on(ProductEvents.CHANGE, this.onProductChange)
                .on(ProductEvents.QTY_CHANGE, this.onQtyChange);
        }

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        //console.log('JoeTest.swatch.detachEvents');
        this.$view
            .off(DomEvents.CHANGE, this.onChange)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);

        EventController
            .off(ProductEvents.CHANGE, this.onProductChange)
            .off(ProductEvents.QTY_CHANGE, this.onQtyChange);

        return this;
    };

        /**
     * @method onActionClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onActionClick = function(event) {
        var action = $(event.target).data('action');

        if (action) {
            event.preventDefault();
            event.stopPropagation();

            EventController
                .emit(ActionEvents.HIDE_ALL)
                .emit(action, this.update);
        }
    };

     $(window).resize(function() {
     // var model = this.model;
        var a = $('#personalization_logo').offset();
        if(a){
        var tp = a.top;
        var lt = a.left;
        $('.popover').offset({top: tp+32 ,left: lt-340});
        $('.arrow').offset({top: tp+25,left:lt-2});
        }
    });

         /**
     * @method onButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    
    proto.onButtonClick = function(event) {
        var model = this.model;
        var info = model.info;
        var options = model.options;
        var construction = this.construction;
        //console.log('jiby');

        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#envelope_help').popover({
                html: true,
                trigger: "manual",
                content: function () {
                    return $('#envelope_popup_model').html();
                }
                // content: function() {
                //     return $('#per_popup').html();
                // },
            });

            //console.log('jiby', $('#popup_model').html());
            if (!cv_flag) {
                $('#envelope_help').popover('show');


                if (!$('#reverse,#envelope_link').is(':visible')) {
                    $('#reverse,#envelope_link').first().show();
                }


            } else {
                $('#envelope_help').popover('hide');
            }
            cv_flag = !cv_flag;

            $('#envelope_content #CVCloseIcon').on('click', function (e) {
                cv_flag = false;
                $('#envelope_help').popover('hide');
                $('#reverse,#envelope_link').hide();
            });
        }
    };

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function() {
        //console.log('JoeTest.swatch.onChange.previous: ' + this.model.getValue());
        //console.log('JoeTest.swatch.onChange.new: ' + this.$view.find('input:checked').val());
        var value = this.$view.find('input:checked').val();
        //console.log('JoeTest.swatch.onChange.options: ' + JSON.stringify(this.model.options, undefined, 4));

        if(this.model.id == 'envelope') {
            SwatchModel.peelAndSeal = value;
            EventController.emit(ProductEvents.ENVELOPE_UPGRADE_CHANGE, value);
        }
        this.model.setValue(value);
        this.isValid();
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.skuId = product.query.skuId;
        this.plmfpf = product.getPLMFPF();
        this.pdtQty = parseInt(product.getQuantityValue());
        this.render();
    };

    /**
     * @method onQtyChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onQtyChange = function() {
        this.render();
    };

    return SwatchQuestionController;
});
